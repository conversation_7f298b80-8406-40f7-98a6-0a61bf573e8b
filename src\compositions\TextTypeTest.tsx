import React from 'react';
import { AbsoluteFill, useCurrentFrame, useVideoConfig } from 'remotion';
import TextType from '../components/reactbits/textanimations/TextType';

export const TextTypeTest: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  return (
    <AbsoluteFill className="bg-gray-900 flex items-center justify-center">
      <div className="text-center space-y-8">
        {/* 标题 */}
        <h1 className="text-4xl font-bold text-white mb-8">
          Frame-driven TextType Test
        </h1>
        
        {/* 帧信息 */}
        <div className="text-white text-lg mb-8">
          Frame: {frame} | Time: {(frame / fps).toFixed(2)}s
        </div>
        
        {/* 基于帧的打字机效果 */}
        <div className="space-y-6">
          <TextType
            text="Hello, Frame-driven World!"
            className="text-3xl font-mono text-green-400"
            useFrameBasedAnimation={true}
            startFrame={30}
            duration={120}
            showCursor={true}
            cursorCharacter="|"
            cursorBlinkDuration={0.5}
            cursorClassName="text-green-400"
          />
          
          <TextType
            text={["First sentence", "Second sentence", "Third sentence"]}
            className="text-2xl font-mono text-blue-400"
            useFrameBasedAnimation={true}
            startFrame={180}
            duration={180}
            showCursor={true}
            cursorCharacter="_"
            cursorBlinkDuration={0.8}
            cursorClassName="text-blue-400"
          />
        </div>
        
        {/* 传统时间驱动的对比 */}
        <div className="mt-12 p-4 border border-gray-600 rounded">
          <h3 className="text-white text-lg mb-4">Traditional Time-based (for comparison):</h3>
          <TextType
            text="This uses traditional time-based animation"
            className="text-xl font-mono text-yellow-400"
            useFrameBasedAnimation={false}
            typingSpeed={100}
            showCursor={true}
            cursorCharacter="|"
            cursorBlinkDuration={0.6}
            cursorClassName="text-yellow-400"
          />
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default TextTypeTest;
