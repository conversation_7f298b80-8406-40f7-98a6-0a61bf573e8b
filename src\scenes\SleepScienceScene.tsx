import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import TextType from '../components/reactbits/textanimations/TextType';
import CountUp from '../components/reactbits/textanimations/CountUp';
import PixelTrail from '../components/reactbits/animotions/PixelTrail';

const SleepScienceScene: React.FC<SceneProps> = ({ durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 标题淡出动画 (320帧开始淡出)
  const titleFadeOut = interpolate(currentFrame, [150, 170], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点动画 - 先居中显示，然后滑动到左上角
  const corePointCenterPhase = interpolate(currentFrame, [150, 240], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点滑动到左上角的动画 (240-300帧)
  const corePointSlidePhase = interpolate(currentFrame, [240, 300], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点位置和大小变化 - 滑动到最上方
  const corePointTranslateX = interpolate(corePointSlidePhase, [0, 1], [0, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const corePointTranslateY = interpolate(corePointSlidePhase, [0, 1], [0, -35], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const corePointScale = interpolate(corePointSlidePhase, [0, 1], [1, 0.8], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个要点的依次出现 - 在核心观点滑动完成后开始
  const point1FadeIn = interpolate(currentFrame, [360, 390], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const point2FadeIn = interpolate(currentFrame, [390, 420], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const point3FadeIn = interpolate(currentFrame, [420, 450], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 例子动画
  const exampleFadeIn = interpolate(currentFrame, [540, 570], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const exampleScale = interpolate(currentFrame, [540, 570], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个要点在例子出现时淡出
  const pointsFadeOut = interpolate(currentFrame, [540, 570], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="flex flex-col items-center justify-center text-white relative">
      {/* 主标题 - 先居中显示，然后滑动到上方 */}
      <div
        className="absolute z-20 w-full h-full flex justify-center items-center"
        style={{
          opacity: titleFadeOut,
          transformOrigin: 'center center',
          transition: 'none',
        }}
      >
        <div className="text-center max-w-4xl px-8">
          <TextType
            text="但睡眠的核心不是早，而是规律"
            className="text-6xl md:text-8xl lg:text-9xl font-semibold text-center leading-tight"
            startFrame={0}
            duration={90}
            showCursor={true}
          />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col h-full">

        {/* 核心观点 - 先居中显示，然后滑动到左上角 */}
        <div
          className="absolute z-15 w-full h-full flex justify-center items-center"
          style={{
            opacity: corePointCenterPhase * pointsFadeOut,
            transform: `translate(${corePointTranslateX}vw, ${corePointTranslateY}vh) scale(${corePointScale})`,
            transformOrigin: 'center center',
            transition: 'none',
          }}
        >
          <div className="text-center max-w-6xl px-4 overflow-visible">
            <div className="text-4xl md:text-5xl lg:text-6xl text-yellow-300 font-medium mb-4 whitespace-nowrap">
              研究表明，成年人平均需要
              <span className="mx-2 text-4xl md:text-5xl lg:text-6xl font-bold text-orange-300">
                <CountUp
                  from={0}
                  to={7}
                  useFrameBasedAnimation={true}
                  startFrame={180}
                  frameDuration={60}
                  ease="easeOut"
                  className="inline-block"
                />
                –
                <CountUp
                  from={0}
                  to={9}
                  useFrameBasedAnimation={true}
                  startFrame={170}
                  frameDuration={70}
                  ease="easeOut"
                  className="inline-block"
                />
              </span>
              小时的睡眠
            </div>
          </div>
        </div>

        {/* 三个要点 */}
        <div
          className="flex-1 flex flex-col px-16 space-y-6"
          style={{
            marginTop: '40vh',
            opacity: pointsFadeOut
          }}
        >
          <TextType
            text="而最重要的不是几点睡，而是"
            className="text-xl md:text-5xl text-gray-300"
            startFrame={300}
            duration={60}
            showCursor={true}
          />
          {/* 要点1 */}
          <div style={{ height: '500px', position: 'relative', overflow: 'hidden' }}>
            <PixelTrail
              gridSize={50}
              trailSize={0.1}
              maxAge={250}
              interpolate={5}
              color="#fff"
              gooeyFilter={{ id: "custom-goo-filter", strength: 2 }}
            />
          </div>
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/30"
            style={{ opacity: point1FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-blue-200">
                1、入睡和起床时间是否稳定
              </p>
            </div>
          </div>

          {/* 要点2 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-purple-400/30"
            style={{ opacity: point2FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-purple-200">
                2、是否保证足够的睡眠时长
              </p>
            </div>
          </div>

          {/* 要点3 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-indigo-400/30"
            style={{ opacity: point3FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-indigo-200">
                3、是否获得完整的深睡和快速眼动睡眠
              </p>
            </div>
          </div>
        </div>

        {/* 例子说明 - 居中显示 */}
        <div
          className="absolute inset-0 flex items-center justify-center px-8 z-30"
          style={{
            opacity: exampleFadeIn,
            transform: `scale(${exampleScale})`,
          }}
        >
          <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-8 border border-green-400/30 max-w-4xl">
            <p className="text-6xl md:text-6xl font-semibold text-white text-center">
              如果你每天 1 点睡、9 点起，保持规律，也比"今天 10 点睡，明天 2 点睡"更健康
            </p>
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default SleepScienceScene;