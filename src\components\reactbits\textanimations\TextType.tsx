"use client";

import { ElementType, useEffect, useRef, createElement, useMemo } from "react";
import { gsap } from "gsap";
import { useCurrentFrame, interpolate, useVideoConfig } from "remotion";

interface TextTypeProps {
  className?: string;
  showCursor?: boolean;
  hideCursorWhileTyping?: boolean;
  cursorCharacter?: string | React.ReactNode;
  cursorBlinkDuration?: number;
  cursorClassName?: string;
  text: string | string[];
  as?: ElementType;
  reverseMode?: boolean;
  textColors?: string[];
  onSentenceComplete?: (sentence: string, index: number) => void;
  // Remotion Frame 控制参数
  startFrame?: number;
  endFrame?: number;
  duration?: number; // 总动画持续时间（帧数）
  charDuration?: number; // 单个字符动画持续时间（帧数）
  autoAdjustSpeed?: boolean; // 是否根据持续时间自动调整速度
}

const TextType = ({
  text,
  as: Component = "div",
  className = "",
  showCursor = true,
  hideCursorWhileTyping = false,
  cursorCharacter = "|",
  cursorClassName = "",
  cursorBlinkDuration = 0.5,
  textColors = [],
  onSentenceComplete,
  reverseMode = false,
  // Remotion Frame 参数
  startFrame = 0,
  endFrame,
  duration,
  charDuration = 3,
  autoAdjustSpeed = true,
  ...props
}: TextTypeProps & React.HTMLAttributes<HTMLElement>) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const currentTextIndex = 0; // 简化为只支持单个文本
  const cursorRef = useRef<HTMLSpanElement>(null);
  const containerRef = useRef<HTMLElement>(null);

  const textArray = useMemo(() => (Array.isArray(text) ? text : [text]), [text]);

  // 计算基于帧的动画参数
  const getFrameAnimationParams = () => {
    const currentText = textArray[currentTextIndex];
    const textLength = currentText.length;

    // 如果提供了 duration，自动计算其他参数
    if (duration && autoAdjustSpeed) {
      const calculatedEndFrame = startFrame + duration;

      return {
        actualStartFrame: startFrame,
        actualEndFrame: calculatedEndFrame,
      };
    }

    // 如果提供了 endFrame
    if (endFrame !== undefined) {
      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
      };
    }

    // 默认值
    const defaultDuration = textLength * charDuration;
    return {
      actualStartFrame: startFrame,
      actualEndFrame: startFrame + defaultDuration,
    };
  };

  const getCurrentTextColor = () => {
    if (textColors.length === 0) return "#ffffff";
    return textColors[currentTextIndex % textColors.length];
  };

  // 基于 frame 的光标闪烁动画
  useEffect(() => {
    if (showCursor && cursorRef.current) {
      // 只在动画开始后才显示光标
      if (frame < startFrame) {
        gsap.set(cursorRef.current, { opacity: 0 });
        return;
      }

      // 将 cursorBlinkDuration 从秒转换为帧数，使用实际的 fps
      const blinkDurationInFrames = cursorBlinkDuration * fps;
      const blinkCycle = blinkDurationInFrames * 2; // 完整的闪烁周期（显示+隐藏）

      // 从 startFrame 开始计算闪烁周期
      const effectiveFrame = Math.max(0, frame - startFrame);
      const cyclePosition = effectiveFrame % blinkCycle;

      // 使用 interpolate 创建平滑的淡入淡出效果
      const opacity = interpolate(
        cyclePosition,
        [0, blinkDurationInFrames * 0.1, blinkDurationInFrames * 0.9, blinkDurationInFrames, blinkDurationInFrames * 1.1, blinkDurationInFrames * 1.9, blinkCycle],
        [1, 1, 1, 0, 0, 0, 1],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
        }
      );

      gsap.set(cursorRef.current, { opacity });
    }
  }, [showCursor, cursorBlinkDuration, frame, fps, startFrame]);

  // 计算当前显示的文本（基于帧的动画逻辑）
  const getDisplayedText = () => {
    const { actualStartFrame, actualEndFrame } = getFrameAnimationParams();
    const currentText = textArray[currentTextIndex];
    const processedText = reverseMode
      ? currentText.split("").reverse().join("")
      : currentText;

    // 计算当前应该显示的字符数
    const progress = interpolate(
      frame,
      [actualStartFrame, actualEndFrame],
      [0, processedText.length],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
      }
    );

    const targetCharIndex = Math.floor(progress);
    return processedText.slice(0, targetCharIndex);
  };

  const displayedText = getDisplayedText();

  // 检查动画是否完成并触发回调
  useEffect(() => {
    const { actualEndFrame } = getFrameAnimationParams();
    if (frame >= actualEndFrame && onSentenceComplete) {
      const currentText = textArray[currentTextIndex];
      onSentenceComplete(currentText, currentTextIndex);
    }
  }, [frame, currentTextIndex, textArray, onSentenceComplete]);

  const shouldHideCursor =
    hideCursorWhileTyping &&
    displayedText.length < textArray[currentTextIndex].length;

  return createElement(
    Component,
    {
      ref: containerRef,
      className: `inline-block whitespace-pre-wrap tracking-tight ${className}`,
      ...props,
    },
    <span className="inline" style={{ color: getCurrentTextColor() }}>
      {displayedText}
    </span>,
    showCursor && (
      <span
        ref={cursorRef}
        className={`ml-1 inline-block opacity-100 ${shouldHideCursor ? "hidden" : ""} ${cursorClassName}`}
      >
        {cursorCharacter}
      </span>
    )
  );
};

export default TextType;
