import React from 'react';
import { AbsoluteFill, Sequence, useVideoConfig } from 'remotion';
import OpeningScene from './scenes/OpeningScene';
import SleepScienceScene from './scenes/SleepScienceScene';
import EarlySleepBenefitsScene from './scenes/EarlySleepBenefitsScene';
import ConclusionScene from './scenes/ConclusionScene';
import Aurora from './components/reactbits/backgrounds/Aurora';

interface SceneTiming {
  start: number;
  duration: number;
}

interface SceneTimings {
  opening: SceneTiming;
  sleepScience: SceneTiming;
  benefits: SceneTiming;
  mythBusting: SceneTiming;
  conclusion: SceneTiming;
}

export const MyComposition: React.FC = () => {
  const { fps } = useVideoConfig();

  // 各场景时长（以帧为单位）
  const sceneDurations = {
    opening: 6 * fps,        // 6秒
    sleepScience: 23 * fps,  // 23秒
    benefits: 20 * fps,      // 20秒
    conclusion: 33 * fps,    // 33秒
  };

  // 计算各场景开始时间
  let currentFrame = 0;
  const sceneTimings: SceneTimings = {
    opening: { start: currentFrame, duration: sceneDurations.opening },
    sleepScience: { start: 0, duration: 0 },
    benefits: { start: 0, duration: 0 },
    mythBusting: { start: 0, duration: 0 },
    conclusion: { start: 0, duration: 0 },
  };
  currentFrame += sceneDurations.opening;

  sceneTimings.sleepScience = { start: currentFrame, duration: sceneDurations.sleepScience };
  currentFrame += sceneDurations.sleepScience;

  sceneTimings.benefits = { start: currentFrame, duration: sceneDurations.benefits };
  currentFrame += sceneDurations.benefits;

  sceneTimings.conclusion = { start: currentFrame, duration: sceneDurations.conclusion };

  return (
    <AbsoluteFill className="bg-black relative overflow-hidden">
      {/* 统一的背景动画 - 贯穿整个视频 */}
      <div className="absolute inset-0 opacity-60">
        <Aurora
          colorStops={["#5227FF", "#7cff67", "#ff6b6b", "#4ecdc4"]}
          amplitude={1.2}
          blend={0.7}
          speed={0.8}
        />
      </div>

      {/* 开场引入 */}
      <Sequence
        from={sceneTimings.opening.start}
        durationInFrames={sceneTimings.opening.duration}
      >
        <OpeningScene
          frame={0}
          durationInFrames={sceneTimings.opening.duration}
        />
      </Sequence>

      {/* 第一部分：睡眠科学 */}
      <Sequence
        from={sceneTimings.sleepScience.start}
        durationInFrames={sceneTimings.sleepScience.duration}
      >
        <SleepScienceScene
          frame={0}
          durationInFrames={sceneTimings.sleepScience.duration}
        />
      </Sequence>

      {/* 第二部分：早睡益处 */}
      <Sequence
        from={sceneTimings.benefits.start}
        durationInFrames={sceneTimings.benefits.duration}
      >
        <EarlySleepBenefitsScene
          frame={0}
          durationInFrames={sceneTimings.benefits.duration}
        />
      </Sequence>

      {/* 结尾升华 */}
      <Sequence
        from={sceneTimings.conclusion.start}
        durationInFrames={sceneTimings.conclusion.duration}
      >
        <ConclusionScene
          frame={0}
          durationInFrames={sceneTimings.conclusion.duration}
        />
      </Sequence>
    </AbsoluteFill>
  );
};
